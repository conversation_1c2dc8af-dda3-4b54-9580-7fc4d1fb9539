from unittest.mock import Mock, patch

import pytest

from custom.enums import ShelfType
from custom.enums.colors import Sofa01Color
from gallery.enums import SottyModuleType
from gallery.services.sotty_single_module import SottySingleModuleRepository


@pytest.mark.django_db
class TestSottySingleModuleRepository:

    @pytest.mark.parametrize(
        ('modules', 'expected_module_type'),
        [
            ([{'type': 'armrest'}], SottyModuleType.ARMREST),
            ([{'type': 'open_end'}, {'type': 'seater'}], SottyModuleType.END_FOOTREST),
            ([{'type': 'split'}], SottyModuleType.EXTENDED_CHAISE_LONGUE),
        ]
    )
    def test_get_module_type(self, sotty_factory, modules, expected_module_type):
        sotty = sotty_factory(
            configurator_params={
                'layout': [
                    {
                        'modules': modules
                    }
                ]
            }
        )

        module_type = SottySingleModuleRepository.get_module_type(sotty)
        assert module_type == expected_module_type

    @patch('gallery.services.sotty_single_module.Sotty.objects.get_cover_presets')
    @patch(
        'gallery.services.sotty_single_module.SottySingleModuleRepository.get_module_type',
        return_value=SottyModuleType.SEATER
    )
    def test_get_module_type_siblings(self, _, mock, sotty_factory):
        mock.return_value = [
            {'id': 1, 'module_type': SottyModuleType.SEATER},
            {'id': 2, 'module_type': SottyModuleType.SEATER},
            {'id': 3, 'module_type': SottyModuleType.FOOTREST},
            {'id': 4, 'module_type': SottyModuleType.EXTENDED_CHAISE_LONGUE},
        ]
        repository = SottySingleModuleRepository(sotty_factory(), use_covers=True)

        siblings = repository.get_module_type_siblings(use_covers=True)

        assert {s['id'] for s in siblings} == {1, 2}

    @patch('gallery.services.sotty_single_module.Sotty.objects.get_cover_presets')
    def test_get_attributes_map(self, mock, sotty_factory):
        """Test get_attributes_map returns correct structure."""
        sotty = sotty_factory(
            materials=[Sofa01Color.REWOOL2_BROWN],
            width=1_000,
            depth=1_625,
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {'type': SottyModuleType.CHAISE_LONGUE}
                        ]
                    }
                ]
            }
        )
        different_material = {
            'id': 123,
            'module_type': SottyModuleType.CHAISE_LONGUE,
            'material': Sofa01Color.REWOOL2_OLIVE_GREEN,
            'width': 1_000,
            'depth': 1_625
        }
        different_width_1 = {
            'id': 124,
            'module_type': SottyModuleType.CHAISE_LONGUE,
            'material': Sofa01Color.REWOOL2_BROWN,
            'width': 875,
            'depth': 1_625
        }
        different_width_2 = {
            'id': 125,
            'module_type': SottyModuleType.CHAISE_LONGUE,
            'material': Sofa01Color.REWOOL2_BROWN,
            'width': 1_125,
            'depth': 1_625
        }
        same_attributes = {
            'id': 126,
            'module_type': SottyModuleType.CHAISE_LONGUE,
            'material': Sofa01Color.REWOOL2_BROWN,
            'width': 1_000,
            'depth': 1_625
        }
        wrong_module_type = {
            'id': 999,
            'module_type': SottyModuleType.CHAISE_LONGUE,
            'material': Sofa01Color.REWOOL2_BROWN,
            'width': 1_000,
            'depth': 1_625
        }
        mock.return_value = [
            different_material,
            different_width_1,
            different_width_2,
            same_attributes,
            wrong_module_type,
        ]
        repository = SottySingleModuleRepository(sotty, use_covers=True)
        attributes_map = repository.get_attributes_map()

        from pprint import pprint
        pprint(attributes_map)
        # Check structure
        # assert 'material' in attributes_map
        # assert 'width' in attributes_map
        # assert 'depth' in attributes_map
        # assert 'active_material' in attributes_map
        # assert 'active_width' in attributes_map
        # assert 'active_depth' in attributes_map
        #
        # # Check active values
        # assert attributes_map['active_material'] == sotty.material
        # assert attributes_map['active_width'] == sotty.width
        # assert attributes_map['active_depth'] == sotty.depth
        #
        # # Check that material, width, depth are dictionaries
        # assert isinstance(attributes_map['material'], dict)
        # assert isinstance(attributes_map['width'], dict)
        # assert isinstance(attributes_map['depth'], dict)

    def test_find_variation_exact_match(self, sotty_factory):
        """Test _find_variation finds exact match."""
        sotty = sotty_factory(
            material=1,
            width=1000,
            depth=1125,
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {'type': SottyModuleType.SEATER.value}
                        ]
                    }
                ]
            }
        )

        mock_presets = [
            {'id': 1, 'module_type': SottyModuleType.SEATER, 'material': 1, 'width': 1000, 'depth': 1125},
            {'id': 2, 'module_type': SottyModuleType.SEATER, 'material': 2, 'width': 1000, 'depth': 1125},
        ]

        with patch.object(sotty.__class__.objects, 'get_cover_presets', return_value=mock_presets):
            repository = SottySingleModuleRepository(sotty, use_covers=True)

            # Should find sibling with material=2 but same width and depth
            result = repository._find_variation('material', 2)
            assert result == 2

    def test_find_variation_no_match(self, sotty_factory):
        """Test _find_variation returns None when no match found."""
        sotty = sotty_factory(
            material=1,
            width=1000,
            depth=1125,
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {'type': SottyModuleType.SEATER.value}
                        ]
                    }
                ]
            }
        )

        mock_presets = [
            {'id': 1, 'module_type': SottyModuleType.SEATER, 'material': 1, 'width': 1000, 'depth': 1125},
        ]

        with patch.object(sotty.__class__.objects, 'get_cover_presets', return_value=mock_presets):
            repository = SottySingleModuleRepository(sotty, use_covers=True)

            # Should not find sibling with material=2
            result = repository._find_variation('material', 2)
            assert result is None

    def test_find_variation_width_change(self, sotty_factory):
        """Test _find_variation for width attribute."""
        sotty = sotty_factory(
            material=1,
            width=1000,
            depth=1125,
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {'type': SottyModuleType.SEATER.value}
                        ]
                    }
                ]
            }
        )

        mock_presets = [
            {'id': 1, 'module_type': SottyModuleType.SEATER, 'material': 1, 'width': 1000, 'depth': 1125},
            {'id': 2, 'module_type': SottyModuleType.SEATER, 'material': 1, 'width': 875, 'depth': 1125},
        ]

        with patch.object(sotty.__class__.objects, 'get_cover_presets', return_value=mock_presets):
            repository = SottySingleModuleRepository(sotty, use_covers=True)

            # Should find sibling with width=875 but same material and depth
            result = repository._find_variation('width', 875)
            assert result == 2

    def test_find_variation_depth_change(self, sotty_factory):
        """Test _find_variation for depth attribute."""
        sotty = sotty_factory(
            material=1,
            width=1000,
            depth=1125,
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {'type': SottyModuleType.SEATER.value}
                        ]
                    }
                ]
            }
        )

        mock_presets = [
            {'id': 1, 'module_type': SottyModuleType.SEATER, 'material': 1, 'width': 1000, 'depth': 1125},
            {'id': 2, 'module_type': SottyModuleType.SEATER, 'material': 1, 'width': 1000, 'depth': 1000},
        ]

        with patch.object(sotty.__class__.objects, 'get_cover_presets', return_value=mock_presets):
            repository = SottySingleModuleRepository(sotty, use_covers=True)

            # Should find sibling with depth=1000 but same material and width
            result = repository._find_variation('depth', 1000)
            assert result == 2

    def test_attrs_constant(self):
        """Test that _ATTRS constant contains expected attributes."""
        assert SottySingleModuleRepository._ATTRS == {'material', 'width', 'depth'}

    def test_get_attributes_map_with_valid_widths_and_depths(self, sotty_factory):
        """Test get_attributes_map uses module_type valid_widths and valid_depths."""
        sotty = sotty_factory(
            materials=[Sofa01Color.REWOOL2_BROWN],
            width=875,
            depth=1000,
            material=1,
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {'type': SottyModuleType.CHAISE_LONGUE.value}
                        ]
                    }
                ]
            }
        )

        mock_presets = []

        with patch.object(sotty.__class__.objects, 'get_cover_presets', return_value=mock_presets):
            with patch.object(ShelfType.SOFA_TYPE01, 'colors', [Mock(value=1), Mock(value=2)]):
                repository = SottySingleModuleRepository(sotty, use_covers=True)
                attributes_map = repository.get_attributes_map()

                # Check that width uses CHAISE_LONGUE valid_widths (875, 1000, 1125)
                expected_widths = SottyModuleType.CHAISE_LONGUE.valid_widths
                assert set(attributes_map['width'].keys()) == set(expected_widths)

                # Check that depth uses CHAISE_LONGUE valid_depths (empty tuple, so no depths)
                expected_depths = SottyModuleType.CHAISE_LONGUE.valid_depths
                assert set(attributes_map['depth'].keys()) == set(expected_depths)

    def test_get_attributes_map_with_footrest_module(self, sotty_factory):
        """Test get_attributes_map with FOOTREST module type."""
        sotty = sotty_factory(
            materials=[Sofa01Color.REWOOL2_BROWN],
            width=625,
            depth=1000,
            material=1,
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {'type': SottyModuleType.FOOTREST.value}
                        ]
                    }
                ]
            }
        )

        mock_presets = []

        with patch.object(sotty.__class__.objects, 'get_cover_presets', return_value=mock_presets):
            with patch.object(ShelfType.SOFA_TYPE01, 'colors', [Mock(value=1)]):
                repository = SottySingleModuleRepository(sotty, use_covers=True)
                attributes_map = repository.get_attributes_map()

                # Check that width uses FOOTREST valid_widths
                expected_widths = SottyModuleType.FOOTREST.valid_widths
                assert set(attributes_map['width'].keys()) == set(expected_widths)

                # Check that depth uses FOOTREST valid_depths
                expected_depths = SottyModuleType.FOOTREST.valid_depths
                assert set(attributes_map['depth'].keys()) == set(expected_depths)

    def test_find_variation_multiple_matches_returns_first(self, sotty_factory):
        """Test _find_variation returns first match when multiple siblings match."""
        sotty = sotty_factory(
            material=1,
            width=1000,
            depth=1125,
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {'type': SottyModuleType.SEATER.value}
                        ]
                    }
                ]
            }
        )

        mock_presets = [
            {'id': 1, 'module_type': SottyModuleType.SEATER, 'material': 1, 'width': 1000, 'depth': 1125},
            {'id': 2, 'module_type': SottyModuleType.SEATER, 'material': 2, 'width': 1000, 'depth': 1125},
            {'id': 3, 'module_type': SottyModuleType.SEATER, 'material': 2, 'width': 1000, 'depth': 1125},
        ]

        with patch.object(sotty.__class__.objects, 'get_cover_presets', return_value=mock_presets):
            repository = SottySingleModuleRepository(sotty, use_covers=True)

            # Should return first match (id=2)
            result = repository._find_variation('material', 2)
            assert result == 2

    def test_find_variation_partial_match_rejected(self, sotty_factory):
        """Test _find_variation rejects partial matches."""
        sotty = sotty_factory(
            material=1,
            width=1000,
            depth=1125,
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {'type': SottyModuleType.SEATER.value}
                        ]
                    }
                ]
            }
        )

        mock_presets = [
            {'id': 1, 'module_type': SottyModuleType.SEATER, 'material': 1, 'width': 1000, 'depth': 1125},
            # This sibling matches material=2 but has different depth
            {'id': 2, 'module_type': SottyModuleType.SEATER, 'material': 2, 'width': 1000, 'depth': 1000},
        ]

        with patch.object(sotty.__class__.objects, 'get_cover_presets', return_value=mock_presets):
            repository = SottySingleModuleRepository(sotty, use_covers=True)

            # Should not find match because depth differs
            result = repository._find_variation('material', 2)
            assert result is None

    def test_empty_siblings_list(self, sotty_factory):
        """Test behavior when siblings list is empty."""
        sotty = sotty_factory(
            material=1,
            width=1000,
            depth=1125,
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {'type': SottyModuleType.SEATER.value}
                        ]
                    }
                ]
            }
        )

        with patch.object(sotty.__class__.objects, 'get_cover_presets', return_value=[]):
            repository = SottySingleModuleRepository(sotty, use_covers=True)

            assert repository.siblings == []

            # All variations should return None
            result = repository._find_variation('material', 2)
            assert result is None

            # get_attributes_map should still work but all values should be None
            with patch.object(ShelfType.SOFA_TYPE01, 'colors', [Mock(value=1), Mock(value=2)]):
                attributes_map = repository.get_attributes_map()

                # All material variations should be None
                for material_value in attributes_map['material'].values():
                    assert material_value is None

                # All width variations should be None
                for width_value in attributes_map['width'].values():
                    assert width_value is None

                # All depth variations should be None
                for depth_value in attributes_map['depth'].values():
                    assert depth_value is None

    @patch.object(ShelfType.SOFA_TYPE01, 'colors', [])
    def test_get_attributes_map_empty_colors(self, sotty_factory):
        """Test get_attributes_map when SOFA_TYPE01.colors is empty."""
        sotty = sotty_factory(
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {'type': SottyModuleType.SEATER.value}
                        ]
                    }
                ]
            }
        )

        with patch.object(sotty.__class__.objects, 'get_cover_presets', return_value=[]):
            repository = SottySingleModuleRepository(sotty, use_covers=True)
            attributes_map = repository.get_attributes_map()

            # Material should be empty dict since no colors
            assert attributes_map['material'] == {}

            # Width and depth should still have values from module_type
            assert len(attributes_map['width']) > 0
            assert len(attributes_map['depth']) >= 0  # Some module types have empty valid_depths

    def test_integration_real_module_types(self, sotty_factory):
        """Integration test with real SottyModuleType values."""
        # Test with different real module types
        test_cases = [
            (SottyModuleType.SEATER, (750, 875, 1000, 1125), (1000, 1125)),
            (SottyModuleType.FOOTREST, (625, 750, 875, 1000, 1125), (1000, 1125)),
            (SottyModuleType.CHAISE_LONGUE, (875, 1000, 1125), ()),
            (SottyModuleType.CORNER, (), (1000, 1125)),
        ]

        for module_type, expected_widths, expected_depths in test_cases:
            sotty = sotty_factory(
                materials=[Sofa01Color.REWOOL2_BROWN],
                width=1000,
                depth=1125,
                material=1,
                configurator_params={
                    'layout': [
                        {
                            'modules': [
                                {'type': module_type.value}
                            ]
                        }
                    ]
                }
            )

            with patch.object(sotty.__class__.objects, 'get_cover_presets', return_value=[]):
                with patch.object(ShelfType.SOFA_TYPE01, 'colors', [Mock(value=1)]):
                    repository = SottySingleModuleRepository(sotty, use_covers=True)

                    assert repository.module_type == module_type

                    # Check that valid_widths and valid_depths match expected values
                    assert repository.module_type.valid_widths == expected_widths
                    assert repository.module_type.valid_depths == expected_depths

                    attributes_map = repository.get_attributes_map()

                    # Verify width and depth keys match module type's valid values
                    assert set(attributes_map['width'].keys()) == set(expected_widths)
                    assert set(attributes_map['depth'].keys()) == set(expected_depths)

    def test_complex_scenario_with_multiple_variations(self, sotty_factory):
        """Test complex scenario with multiple material, width, and depth variations."""
        sotty = sotty_factory(
            materials=[Sofa01Color.REWOOL2_BROWN],
            width=1000,
            depth=1125,
            material=1,
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {'type': SottyModuleType.SEATER.value}
                        ]
                    }
                ]
            }
        )

        # Create comprehensive mock presets covering various combinations
        mock_presets = [
            # Current sotty
            {'id': 1, 'module_type': SottyModuleType.SEATER, 'material': 1, 'width': 1000, 'depth': 1125},
            # Material variations
            {'id': 2, 'module_type': SottyModuleType.SEATER, 'material': 2, 'width': 1000, 'depth': 1125},
            {'id': 3, 'module_type': SottyModuleType.SEATER, 'material': 3, 'width': 1000, 'depth': 1125},
            # Width variations
            {'id': 4, 'module_type': SottyModuleType.SEATER, 'material': 1, 'width': 875, 'depth': 1125},
            {'id': 5, 'module_type': SottyModuleType.SEATER, 'material': 1, 'width': 750, 'depth': 1125},
            # Depth variations
            {'id': 6, 'module_type': SottyModuleType.SEATER, 'material': 1, 'width': 1000, 'depth': 1000},
            # Mixed variations (should not be found by _find_variation)
            {'id': 7, 'module_type': SottyModuleType.SEATER, 'material': 2, 'width': 875, 'depth': 1000},
            # Different module type (should be filtered out)
            {'id': 8, 'module_type': SottyModuleType.CORNER, 'material': 1, 'width': 1000, 'depth': 1125},
        ]

        with patch.object(sotty.__class__.objects, 'get_cover_presets', return_value=mock_presets):
            with patch.object(ShelfType.SOFA_TYPE01, 'colors', [Mock(value=1), Mock(value=2), Mock(value=3)]):
                repository = SottySingleModuleRepository(sotty, use_covers=True)

                # Should only include SEATER module types
                assert len(repository.siblings) == 7  # Excludes the CORNER module

                attributes_map = repository.get_attributes_map()

                # Check material variations
                assert attributes_map['material'][1] is None  # Current material, no variation needed
                assert attributes_map['material'][2] == 2     # Different material, same w/d
                assert attributes_map['material'][3] == 3     # Different material, same w/d

                # Check width variations (material=1, depth=1125)
                assert attributes_map['width'][1000] is None  # Current width
                assert attributes_map['width'][875] == 4      # Different width, same m/d
                assert attributes_map['width'][750] == 5      # Different width, same m/d
                assert attributes_map['width'][1125] is None  # No preset with this width

                # Check depth variations (material=1, width=1000)
                assert attributes_map['depth'][1125] is None  # Current depth
                assert attributes_map['depth'][1000] == 6     # Different depth, same m/w
