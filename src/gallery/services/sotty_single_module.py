from typing import (
    Iterable,
    Mapping,
)

from custom.enums import ShelfType
from gallery.enums import SottyModuleType
from gallery.models import Sotty


class SottySingleModuleRepository:
    _ATTRS = {'material', 'width', 'depth'}

    def __init__(self, sotty: Sotty, use_covers: bool):
        self.sotty = sotty
        self.module_type = self.get_module_type(sotty)
        self.siblings = self.get_module_type_siblings(use_covers)

    @staticmethod
    def get_module_type(sotty: Sotty) -> SottyModuleType:
        return sotty.get_module_types()[0]

    def get_module_type_siblings(self, use_covers: bool) -> list[dict]:
        presets = Sotty.objects.get_cover_presets() if use_covers else []
        return [
            preset for preset in presets if preset['module_type'] == self.module_type
        ]

    def get_attributes_map(self) -> dict[str, dict[int, int | None]]:
        """
        Build a mapping of every allowed *material*, *width* and *depth*
        value to the **id** of the first sibling that differs in the other
        two attributes—if such a sibling exists—otherwise ``None``.

        Example
        -------
        {
            "material": {1: 11, 2: 42},
            "width":    {100: 7, 120: None},
            "depth":    {60: 8, 80: 9},
        }
        """

        # values we need to iterate over for each attribute
        candidates: Mapping[str, Iterable[int]] = {
            'material': [c.value for c in ShelfType.SOFA_TYPE01.colors],
            'width': self.module_type.valid_widths,
            'depth': self.module_type.valid_depths,
        }

        data = {
            attr: {v: self._find_variation(attr, v) for v in values}
            for attr, values in candidates.items()
        }
        data['active_material'] = self.sotty.material
        data['active_width'] = self.sotty.width
        data['active_depth'] = self.sotty.depth
        return data

    def _find_variation(self, attribute: str, value: int) -> int | None:
        """
        Return the *id* of the first sibling that matches *attribute=value*
        while differing in every other attribute; otherwise ``None``.
        """
        others = self._ATTRS - {attribute}

        sibling = next(
            (
                sibling
                for sibling in self.siblings
                if sibling[attribute] == value
                and all(
                    sibling[other] == getattr(self.sotty, other) for other in others
                )
            ),
            None,
        )
        return sibling['id'] if sibling else None
