from decimal import Decimal
from typing import Optional

from django.conf import settings
from django.utils import translation
from django.utils.translation import get_language

import pendulum

from rest_framework import serializers

from abtests.constants import EXTENDED_DELIVERY_TEST_NAME
from custom.enums import (
    LanguageEnum,
    ShelfType,
)
from custom.fields import MillimeterToCentimeterField
from custom.utils.in_memory_cache import expiring_lru_cache
from custom.views import is_ab_test_enabled
from gallery.enums import SottyModuleType
from gallery.models import (
    Jetty,
    Sotty,
    Watty,
)
from gallery.services.sotty_single_module import SottySingleModuleRepository
from gallery.services.prices_for_serializers import (
    RegionCurrencySerializerMixin,
    get_region_price,
    get_region_price_in_euro,
    get_region_price_with_discount,
    get_region_price_with_discount_in_euro,
)
from gallery.slugs import (
    get_seo_title_for_all_colors,
    get_slug_for_all_colors,
)
from gallery.types import FurnitureType
from pricing_v3.omnibus import OmnibusCalculator
from promotions.models import Promotion
from promotions.utils import strikethrough_promo
from regions.models import (
    CurrencyRate,
    RegionRate,
)


class BasePDPSerializer(RegionCurrencySerializerMixin, serializers.ModelSerializer):
    price = serializers.SerializerMethodField()
    price_in_euro = serializers.SerializerMethodField()
    price_with_discount = serializers.SerializerMethodField()
    price_with_discount_in_euro = serializers.SerializerMethodField()
    title = serializers.SerializerMethodField()
    pricing = serializers.SerializerMethodField()
    delivery = serializers.SerializerMethodField()
    delivery_summary = serializers.SerializerMethodField()
    is_global_promo_on = serializers.SerializerMethodField()
    available_assembly = serializers.SerializerMethodField()
    assembly_free = serializers.SerializerMethodField()
    seo_titles = serializers.SerializerMethodField()
    seo_slugs = serializers.SerializerMethodField()
    cardboard = serializers.SerializerMethodField()
    drawers_count = serializers.SerializerMethodField()
    configurator_preview = serializers.SerializerMethodField()
    base_preset = serializers.IntegerField()

    class Meta:
        abstract = True
        fields = (
            'assembly_free',
            'available_assembly',
            'base_preset',
            'cardboard',
            'configurator_type',
            'delivery',
            'delivery_summary',
            'depth',
            'furniture_category',
            'grid_all_colors',
            'height',
            'is_global_promo_on',
            'material',
            'physical_product_version',
            'preview',
            'configurator_preview',
            'price',
            'price_in_euro',
            'price_with_discount',
            'price_with_discount_in_euro',
            'pricing',
            'seo_slugs',
            'seo_titles',
            'shelf_type',
            'title',
            'width',
            'base_preset',
        )

    def _get_extended_delivery_context(self) -> bool:
        try:
            return is_ab_test_enabled(
                request=self.context['request'], codename=EXTENDED_DELIVERY_TEST_NAME
            )
        except KeyError:
            return False

    @property
    def region_calculations_object(self):
        return self.context.get('rco', None)

    def get_delivery(self, obj):
        use_extended_delivery = self._get_extended_delivery_context()
        return obj.get_delivery_time_weeks_range(
            region=self.region, use_extended_delivery=use_extended_delivery
        )

    def get_delivery_summary(self, obj):
        return obj.get_delivery_time_summary()

    def get_price(self, obj) -> Decimal:
        return get_region_price(
            obj,
            self.region,
            region_calculations_object=self.region_calculations_object,
        )

    def get_price_in_euro(self, obj) -> Decimal:
        return get_region_price_in_euro(
            obj,
            self.currency_rate,
            self.region,
            region_calculations_object=self.region_calculations_object,
        )

    def get_price_with_discount(self, obj) -> Decimal:
        return get_region_price_with_discount(
            obj,
            self.region,
            region_calculations_object=self.region_calculations_object,
            promotion=strikethrough_promo(self.region),
        )

    def get_price_with_discount_in_euro(self, obj) -> Decimal:
        return get_region_price_with_discount_in_euro(
            obj,
            self.currency_rate,
            self.region,
            region_calculations_object=self.region_calculations_object,
            promotion=strikethrough_promo(self.region),
        )

    def get_seo_titles(self, obj):
        return get_seo_title_for_all_colors(
            obj,
            self.context.get('language', get_language()),
        )

    @staticmethod
    def get_title(obj):
        return '{} {}'.format(
            obj.furniture_category.translated_name,
            ShelfType(obj.shelf_type).translated_name,
        )

    @staticmethod
    @expiring_lru_cache(ttl=settings.REGIONS_CACHE_TTL_SECONDS)
    def get_currency_rate(currency_id: int) -> Optional[CurrencyRate]:
        currency_rates = CurrencyRate.objects.filter(currency_id=currency_id)
        return currency_rates.first()

    @staticmethod
    @expiring_lru_cache(ttl=settings.REGIONS_CACHE_TTL_SECONDS)
    def get_region_rate(region_id: int) -> Optional[RegionRate]:
        region_rates = RegionRate.objects.filter(region_id=region_id)
        return region_rates.first()

    def get_pricing(self, obj):
        region_rate = self.get_region_rate(self.region.id)
        currency_rate = self.get_currency_rate(self.region.currency_id)

        return {
            'priceeee': float(region_rate.rate * currency_rate.rate),
            'priceeel': self.region.currency_symbol,
            'price_iso': self.region.currency_code,
        }

    def get_is_global_promo_on(self, _):
        from promotions.utils import strikethrough_promo

        return bool(strikethrough_promo(self.region))

    def get_available_assembly(self, obj):
        return self.region.name in settings.ASSEMBLY_REGION_KEYS

    @staticmethod
    def get_assembly_free(obj):
        return obj.is_t03_wardrobe

    def get_seo_slugs(self, obj):
        slugs = {}
        for lang in LanguageEnum:
            with translation.override(language=lang):
                slugs[lang] = get_slug_for_all_colors(
                    obj,
                    self.context.get('language', lang),
                )
        return slugs

    @staticmethod
    def get_configurator_preview(obj) -> None | str:
        if obj.configurator_preview and obj.configurator_preview.image:
            return obj.configurator_preview.image.url


class BaseShelvingPDPSerializer(BasePDPSerializer):
    doors_count = serializers.SerializerMethodField()

    class Meta(BasePDPSerializer.Meta):
        fields = BasePDPSerializer.Meta.fields + (
            'density',
            'pattern_name',
            'property1',
            'doors_count',
            'drawers_count',
        )

    @staticmethod
    def get_doors_count(obj):
        return len(obj.doors)

    @staticmethod
    def get_drawers_count(obj):
        return len(obj.drawers)


class JettyPDPSerializer(BaseShelvingPDPSerializer):
    pattern_name = serializers.CharField(source='get_pattern_name', read_only=True)
    density = serializers.FloatField(source='get_fill_density', read_only=True)

    class Meta:
        model = Jetty
        fields = BaseShelvingPDPSerializer.Meta.fields

    @staticmethod
    def get_cardboard(obj):
        return (
            pendulum.today()
            .on(
                year=obj.owner.date_joined.year,
                month=obj.owner.date_joined.month,
                day=obj.owner.date_joined.day,
            )
            .diff(pendulum.now())
            .in_days()
        )


class SottyPDPSerializer(BasePDPSerializer):
    cardboard = serializers.ReadOnlyField(default=None)
    technical_sketch = serializers.SerializerMethodField()
    gallery_desktop_image = serializers.SerializerMethodField()
    gallery_mobile_image = serializers.SerializerMethodField()
    discount_value = serializers.SerializerMethodField()
    eco_tax = serializers.SerializerMethodField()
    width = MillimeterToCentimeterField()
    depth = MillimeterToCentimeterField()
    height = MillimeterToCentimeterField()

    seating_depth = serializers.IntegerField(
        source='get_seating_depth', read_only=True, allow_null=True
    )
    armrest_height = serializers.IntegerField(
        source='get_armrest_height',
        read_only=True,
    )
    seat_height = serializers.IntegerField(
        source='get_seat_height',
        read_only=True,
    )
    fabric = serializers.CharField(read_only=True)
    omnibus_price = serializers.SerializerMethodField()

    class Meta:
        model = Sotty
        fields = BasePDPSerializer.Meta.fields + (
            'armrests',
            'chaise_longues',
            'corners',
            'footrests',
            'seaters',
            'covers_only',
            'discount_value',
            'technical_sketch',
            'eco_tax',
            'weight',
            'seating_depth',
            'armrest_height',
            'seat_height',
            'fabric',
            'omnibus_price',
            'gallery_desktop_image',
            'gallery_mobile_image',
        )

    @property
    def strikethrough_promotion(self) -> Promotion | None:
        return self.context.get('striketrough_promo')

    @property
    def omnibus_calculator(self) -> OmnibusCalculator:
        return OmnibusCalculator.get_instance(self.region)

    def get_discount_value(self, obj: Sotty) -> int | None:
        """Returns the discount value for a Sotty item if a promotion is active."""
        promotion = self.context['striketrough_promo']
        discount_value = None
        if promotion and promotion.promo_code:
            voucher = promotion.promo_code
            discount_value = voucher.get_discount_value_for_item(obj)
        return discount_value

    @staticmethod
    def get_technical_sketch(obj) -> None | str:
        if obj.technical_sketch and obj.technical_sketch.image:
            return obj.technical_sketch.image.url

    def get_omnibus_price(self, obj: FurnitureType) -> Decimal | None:
        if not self.strikethrough_promotion or not self.region:
            return
        return self.omnibus_calculator.calculate_lowest_price(geometry=obj)

    @staticmethod
    def get_gallery_desktop_image(obj) -> None | str:
        if obj.gallery_desktop_image and obj.gallery_desktop_image.image:
            return obj.gallery_desktop_image.image.url

    @staticmethod
    def get_gallery_mobile_image(obj) -> None | str:
        if obj.gallery_mobile_image and obj.gallery_mobile_image.image:
            return obj.gallery_mobile_image.image.url

    def get_eco_tax(self, obj: Sotty) -> Decimal | None:
        return obj.get_eco_tax(region=self.region) if self.region else None


class SottyCoverPDPSerializer(SottyPDPSerializer):
    attributes = serializers.SerializerMethodField()
    module_name = serializers.SerializerMethodField()

    class Meta:
        model = Sotty
        fields = SottyPDPSerializer.Meta.fields + ('attributes', 'module_name')

    @property
    def repository(self) -> SottySingleModuleRepository:
        return SottySingleModuleRepository(sotty=self.instance, use_covers=True)

    def get_attributes(self, obj: Sotty) -> dict[str, dict[int, int | None]]:
        return self.repository.get_attributes_map()

    def get_module_name(self, obj: Sotty) -> dict[str, dict[int, int | None]]:
        if self.repository.module_type in SottyModuleType.get_footrest_types():
            return SottyModuleType.FOOTREST.label
        return self.repository.module_type.label


class WattyPDPSerializer(BaseShelvingPDPSerializer):
    pattern_name = serializers.CharField(source='get_pattern_name', read_only=True)
    cardboard = serializers.ReadOnlyField(default=None)
    density = serializers.ReadOnlyField(default=None)
    property1 = serializers.ReadOnlyField(default=None)

    class Meta:
        model = Watty
        fields = BaseShelvingPDPSerializer.Meta.fields
